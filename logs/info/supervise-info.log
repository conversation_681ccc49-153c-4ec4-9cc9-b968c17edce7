2025-08-04 16:33:20.357 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9333"]
2025-08-04 16:33:20.387 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 16:33:20.387 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.29
2025-08-04 16:33:20.402 [localhost-startStop-1] INFO  org.apache.catalina.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [E:\java8\java\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;E:\java8\java\bin;C:\Program Files\MySQL\MySQL Server 8.0\bin;E:\mavne\apache-maven-3.6.0\bin;C:\Program Files\TortoiseGit\bin;E:\nvm\nvm;E:\nodejs;E:\nodejs\node_modules;E:\nodejs\node_global;E:\JMeter\apache-jmeter-5.6.3\bin;F:\xftp\;F:\xshell\;E:\git\Git\cmd;F:\PowerShell\7\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;F:\idea2024\IntelliJ IDEA 2024.1\bin;;F:\vscode\Microsoft VS Code\bin;E:\nvm\nvm;E:\nodejs;F:\cursor\cursor\resources\app\bin;F:\CodeBuddy\CodeBuddy\bin;.]
2025-08-04 16:33:20.600 [localhost-startStop-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/supapi] - Initializing Spring embedded WebApplicationContext
2025-08-04 16:33:28.189 [main] INFO  job - 初始化定时任务开始Mon Aug 04 16:33:28 CST 2025
2025-08-04 16:33:29.616 [main] INFO  job - 初始化定时任务结束Mon Aug 04 16:33:29 CST 2025
2025-08-04 16:33:29.833 [main] INFO  c.i.ssp.supervise.utils.excel.ImportConfigUtil - 初始化importConfig....
2025-08-04 16:33:31.224 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
